import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FiX, FiPlus, FiMinus } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import { closeCategoryForm } from '../../redux/slices/categoriesSlice';
import { createCategory, updateCategory } from '../../redux/slices/categoriesSlice';
import './CategoryFormModal.css';

const CategoryFormModal = () => {
  const dispatch = useDispatch();
  const { categoryForm, loading } = useSelector((state) => state.categories);
  const { isOpen, mode, data } = categoryForm;

  // Predefined icons for quick selection
  const predefinedIcons = [
    '🐕', '🐱', '🐾', '🏥', '✂️', '🛁', '🎓', '🏃', '🍖', '🧸',
    '🚗', '🏠', '💊', '🩺', '🦴', '🎾', '🥎', '🪀', '🎪', '🎭'
  ];

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    shortDescription: '',
    icon: '',
    image: '',
    thumbnailImage: '',
    color: '#f8f9fa',
    textColor: '#ffffff',
    order: 0,
    isActive: true,
    isFeatured: false,
    metaTitle: '',
    metaDescription: '',
    tags: [''],
  });

  const [imagePreview, setImagePreview] = useState(null);
  const [thumbnailPreview, setThumbnailPreview] = useState(null);
  const [uploadingImage, setUploadingImage] = useState(false);

  useEffect(() => {
    if (mode === 'edit' && data) {
      setFormData({
        name: data.name || '',
        description: data.description || '',
        shortDescription: data.shortDescription || '',
        icon: data.icon || '',
        image: data.image || '',
        thumbnailImage: data.thumbnailImage || '',
        color: data.color || '#f8f9fa',
        textColor: data.textColor || '#ffffff',
        order: data.order || 0,
        isActive: data.isActive !== undefined ? data.isActive : true,
        isFeatured: data.isFeatured || false,
        metaTitle: data.metaTitle || '',
        metaDescription: data.metaDescription || '',
        tags: data.tags || [''],
      });
      setImagePreview(data.image || null);
      setThumbnailPreview(data.thumbnailImage || null);
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        description: '',
        shortDescription: '',
        icon: '',
        image: '',
        thumbnailImage: '',
        color: '#f8f9fa',
        textColor: '#ffffff',
        order: 0,
        isActive: true,
        isFeatured: false,
        metaTitle: '',
        metaDescription: '',
        tags: [''],
      });
      setImagePreview(null);
      setThumbnailPreview(null);
    }
  }, [mode, data]);

  const predefinedColors = [
    '#f8f9fa', '#e8f5e8', '#e8f0ff', '#ffe8e8', '#fff8e8', 
    '#f8e8ff', '#e8fff8', '#ffe8f8', '#f0f8ff', '#fff5ee'
  ];

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleArrayChange = (field, index, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (field, index) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].filter((_, i) => i !== index)
    }));
  };

  const handleImageUpload = async (file, type) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB');
      return;
    }

    setUploadingImage(true);

    try {
      // For demo purposes, we'll use FileReader to create a data URL
      // In production, you would upload to your image service (AWS S3, Cloudinary, etc.)
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target.result;

        if (type === 'main') {
          setFormData(prev => ({ ...prev, image: imageUrl }));
          setImagePreview(imageUrl);
        } else if (type === 'thumbnail') {
          setFormData(prev => ({ ...prev, thumbnailImage: imageUrl }));
          setThumbnailPreview(imageUrl);
        }
      };
      reader.readAsDataURL(file);

      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
      console.error('Image upload error:', error);
    } finally {
      setUploadingImage(false);
    }
  };

  const removeImage = (type) => {
    if (type === 'main') {
      setFormData(prev => ({ ...prev, image: '' }));
      setImagePreview(null);
    } else if (type === 'thumbnail') {
      setFormData(prev => ({ ...prev, thumbnailImage: '' }));
      setThumbnailPreview(null);
    }
  };

  const handleTagChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.map((tag, i) => i === index ? value : tag)
    }));
  };

  const addTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const removeTag = (index) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name || !formData.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      const categoryData = {
        ...formData,
        order: parseInt(formData.order) || 0,
        tags: formData.tags.filter(tag => tag.trim() !== ''),
      };

      if (mode === 'edit') {
        await dispatch(updateCategory({ id: data._id, data: categoryData })).unwrap();
        toast.success('Category updated successfully');
      } else {
        await dispatch(createCategory(categoryData)).unwrap();
        toast.success('Category created successfully');
      }

      dispatch(closeCategoryForm());
    } catch (error) {
      toast.error(error || 'Failed to save category');
    }
  };

  const handleClose = () => {
    dispatch(closeCategoryForm());
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="modal-container" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="modal-header">
          <h3 className="modal-title">
            {mode === 'edit' ? 'Edit Category' : 'Create New Category'}
          </h3>
          <button
            onClick={handleClose}
            className="modal-close-btn"
            type="button"
          >
            <FiX size={20} />
          </button>
        </div>

        {/* Body */}
        <div className="modal-body">
          <form onSubmit={handleSubmit} className="modal-form">
            {/* Basic Information */}
            <div className="form-section">
              <h4 className="section-title">Basic Information</h4>

              {/* Name and Short Description */}
              <div className="form-grid two-cols">
                <div className="form-field">
                  <label className="field-label required">Name</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="field-input"
                    placeholder="e.g., Pet Sitting"
                    required
                  />
                </div>
                <div className="form-field">
                  <label className="field-label">Short Description</label>
                  <input
                    type="text"
                    name="shortDescription"
                    value={formData.shortDescription}
                    onChange={handleChange}
                    className="field-input"
                    placeholder="Brief one-liner"
                    maxLength={100}
                  />
                </div>
              </div>

              {/* Description */}
              <div className="form-field">
                <label className="field-label required">Description</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className="field-textarea"
                  rows={3}
                  placeholder="Detailed description of this category"
                  required
                />
              </div>
            </div>

            {/* Visual Elements */}
            <div className="form-section">
              <h4 className="section-title">Visual Elements</h4>

              {/* Icon Selection */}
              <div className="form-field">
                <label className="field-label">Icon</label>
                <input
                  type="text"
                  name="icon"
                  value={formData.icon}
                  onChange={handleChange}
                  className="field-input"
                  placeholder="Enter emoji or icon"
                />
                <div className="form-grid" style={{gridTemplateColumns: 'repeat(10, 1fr)', gap: '0.5rem', marginTop: '0.75rem'}}>
                  {predefinedIcons.map((icon, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, icon }))}
                      className={`icon-btn ${formData.icon === icon ? 'selected' : ''}`}
                      style={{
                        padding: '0.5rem',
                        fontSize: '1.125rem',
                        border: formData.icon === icon ? '2px solid var(--primary-color)' : '1px solid var(--border-light)',
                        borderRadius: 'var(--radius-md)',
                        background: formData.icon === icon ? 'var(--primary-color)' : 'var(--bg-primary)',
                        color: formData.icon === icon ? 'var(--text-white)' : 'var(--text-primary)',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              {/* Color Scheme */}
              <div className="color-picker-group">
                <div className="color-picker-field">
                  <label className="field-label">Background Color</label>
                  <input
                    type="color"
                    name="color"
                    value={formData.color}
                    onChange={handleChange}
                    className="color-input"
                  />
                </div>
                <div className="color-picker-field">
                  <label className="field-label">Text Color</label>
                  <input
                    type="color"
                    name="textColor"
                    value={formData.textColor}
                    onChange={handleChange}
                    className="color-input"
                  />
                </div>
                <div className="color-preview" style={{backgroundColor: formData.color, color: formData.textColor}}>
                  Preview
                </div>
              </div>

              {/* Image Uploads */}
              <div className="image-upload-section">
                <div className="form-grid two-cols">
                  {/* Main Image */}
                  <div className="image-upload-field">
                    <label className="field-label">Category Image</label>
                    {imagePreview ? (
                      <div style={{position: 'relative'}}>
                        <img
                          src={imagePreview}
                          alt="Category preview"
                          className="image-preview"
                          style={{width: '100%', height: '120px'}}
                        />
                        <button
                          type="button"
                          onClick={() => removeImage('main')}
                          style={{
                            position: 'absolute',
                            top: '0.5rem',
                            right: '0.5rem',
                            padding: '0.25rem',
                            background: 'var(--error-color)',
                            color: 'var(--text-white)',
                            border: 'none',
                            borderRadius: '50%',
                            cursor: 'pointer'
                          }}
                        >
                          <FiX size={16} />
                        </button>
                      </div>
                    ) : (
                      <div style={{
                        border: '2px dashed var(--border-medium)',
                        borderRadius: 'var(--radius-md)',
                        padding: '1rem',
                        textAlign: 'center',
                        background: 'var(--bg-tertiary)'
                      }}>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleImageUpload(e.target.files[0], 'main')}
                          style={{display: 'none'}}
                          id="main-image-upload"
                        />
                        <label
                          htmlFor="main-image-upload"
                          style={{cursor: 'pointer', color: 'var(--text-secondary)'}}
                        >
                          <div style={{fontSize: '2rem', marginBottom: '0.5rem'}}>📷</div>
                          <div style={{fontSize: '0.875rem'}}>Click to upload image</div>
                        </label>
                      </div>
                    )}
                    <input
                      type="url"
                      name="image"
                      value={formData.image}
                      onChange={handleChange}
                      className="image-upload-input"
                      placeholder="Or enter image URL"
                    />
                  </div>

                  {/* Thumbnail Image */}
                  <div className="image-upload-field">
                    <label className="field-label">Thumbnail Image</label>
                    {thumbnailPreview ? (
                      <div style={{position: 'relative'}}>
                        <img
                          src={thumbnailPreview}
                          alt="Thumbnail preview"
                          className="image-preview"
                          style={{width: '100%', height: '120px'}}
                        />
                        <button
                          type="button"
                          onClick={() => removeImage('thumbnail')}
                          style={{
                            position: 'absolute',
                            top: '0.5rem',
                            right: '0.5rem',
                            padding: '0.25rem',
                            background: 'var(--error-color)',
                            color: 'var(--text-white)',
                            border: 'none',
                            borderRadius: '50%',
                            cursor: 'pointer'
                          }}
                        >
                          <FiX size={16} />
                        </button>
                      </div>
                    ) : (
                      <div style={{
                        border: '2px dashed var(--border-medium)',
                        borderRadius: 'var(--radius-md)',
                        padding: '1rem',
                        textAlign: 'center',
                        background: 'var(--bg-tertiary)'
                      }}>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleImageUpload(e.target.files[0], 'thumbnail')}
                          style={{display: 'none'}}
                          id="thumbnail-image-upload"
                        />
                        <label
                          htmlFor="thumbnail-image-upload"
                          style={{cursor: 'pointer', color: 'var(--text-secondary)'}}
                        >
                          <div style={{fontSize: '2rem', marginBottom: '0.5rem'}}>🖼️</div>
                          <div style={{fontSize: '0.875rem'}}>Click to upload thumbnail</div>
                        </label>
                      </div>
                    )}
                    <input
                      type="url"
                      name="thumbnailImage"
                      value={formData.thumbnailImage}
                      onChange={handleChange}
                      className="image-upload-input"
                      placeholder="Or enter thumbnail URL"
                    />
                  </div>
                </div>
              </div>
            </div>
                </div>
              </div>

            {/* Settings and Metadata */}
            <div className="form-section">
              <h4 className="section-title">Settings & Metadata</h4>

              <div className="form-grid three-cols">
                <div className="form-field">
                  <label className="field-label">Order</label>
                  <input
                    type="number"
                    name="order"
                    value={formData.order}
                    onChange={handleChange}
                    className="field-input"
                    placeholder="0"
                    min="0"
                  />
                </div>

                <div className="checkbox-field">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleChange}
                    className="checkbox-input"
                    id="isActive"
                  />
                  <label htmlFor="isActive" className="checkbox-label">Active</label>
                </div>

                <div className="checkbox-field">
                  <input
                    type="checkbox"
                    name="isFeatured"
                    checked={formData.isFeatured}
                    onChange={handleChange}
                    className="checkbox-input"
                    id="isFeatured"
                  />
                  <label htmlFor="isFeatured" className="checkbox-label">Featured</label>
                </div>
              </div>

              <div className="form-grid two-cols">
                <div className="form-field">
                  <label className="field-label">Meta Title</label>
                  <input
                    type="text"
                    name="metaTitle"
                    value={formData.metaTitle}
                    onChange={handleChange}
                    className="field-input"
                    placeholder="SEO title (max 60 chars)"
                    maxLength={60}
                  />
                </div>

                <div className="form-field">
                  <label className="field-label">Meta Description</label>
                  <textarea
                    name="metaDescription"
                    value={formData.metaDescription}
                    onChange={handleChange}
                    className="field-textarea"
                    placeholder="SEO description (max 160 chars)"
                    maxLength={160}
                    rows={2}
                  />
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="form-section">
              <h4 className="section-title">Tags</h4>
              <div className="tags-section">
                <div className="tags-list">
                  {formData.tags.map((tag, index) => (
                    <div key={index} className="tag-input-row">
                      <input
                        type="text"
                        value={tag}
                        onChange={(e) => handleTagChange(index, e.target.value)}
                        className="tag-input"
                        placeholder="Enter tag"
                      />
                      {formData.tags.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeTag(index)}
                          className="tag-remove-btn"
                        >
                          <FiMinus size={16} />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
                <button
                  type="button"
                  onClick={addTag}
                  className="tag-add-btn"
                >
                  <FiPlus size={16} />
                  Add Tag
                </button>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="modal-footer">
          <button
            type="button"
            onClick={handleClose}
            className="modal-btn secondary"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            className="modal-btn primary"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="spinner" style={{width: '1rem', height: '1rem'}}></div>
                {mode === 'edit' ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              <>
                {mode === 'edit' ? 'Update Category' : 'Create Category'}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};
export default CategoryFormModal;
