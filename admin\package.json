{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "date-fns": "^3.0.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-redux": "^9.0.4", "react-router-dom": "^6.28.0", "recharts": "^2.8.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}